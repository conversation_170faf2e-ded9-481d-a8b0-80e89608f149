import React from 'react';
import { ButtonComponent as <PERSON><PERSON> } from '@bika/ui/button';

export default {
  title: 'Ladle Demo/Simple Button',
};

export const Primary = () => (
  <Button size="md" variant="solid" color="primary">
    Primary Button
  </Button>
);

export const Secondary = () => (
  <Button size="md" variant="outlined" color="neutral">
    Secondary Button
  </Button>
);

export const Large = () => (
  <Button size="lg" variant="solid" color="primary">
    Large Button
  </Button>
);

export const Small = () => (
  <Button size="sm" variant="solid" color="primary">
    Small Button
  </Button>
);

export const Loading = () => (
  <Button size="md" variant="solid" color="primary" loading loadingPosition="start">
    Loading Button
  </Button>
);
