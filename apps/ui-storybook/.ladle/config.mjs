import { defineConfig } from '@ladle/react/lib/app/exports.js';

export default defineConfig({
  stories: 'src/stories/ladle-demo/**/*.stories.{js,jsx,ts,tsx}',
  defaultStory: '',
  port: 6006,
  host: 'localhost',
  base: '/',
  outDir: 'build',
  viteConfig: {
    define: {
      'process.env.__NEXT_IMAGE_OPTS': undefined,
      'process.env.STORYBOOK': undefined,
    },
  },
  addons: {
    theme: {
      enabled: true,
      defaultState: 'light',
    },
    control: {
      enabled: true,
    },
    action: {
      enabled: true,
    },
    source: {
      enabled: true,
    },
    a11y: {
      enabled: true,
    },
  },
});
