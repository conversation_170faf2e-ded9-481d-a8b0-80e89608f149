import React from 'react';
import type { GlobalProvider } from '@ladle/react';

// Import the same CSS files as Storybook
import '@bika/ui/lib_colors.css';
import '@bika/ui/theme.css';
import '@bika/domains/shared/client/styles/tailwind-css-base.css';
import 'simplebar-react/dist/simplebar.min.css';
import '../.storybook/custom.css';
import '../src/tailwind.css';

// Import the same theme provider as Storybook
import { ThemeProvider } from '../.storybook/theme';

export const Provider: GlobalProvider = ({ children, globalState }) => {
  // Set the theme attribute on the html element
  React.useEffect(() => {
    const theme = globalState.theme || 'light';
    document.documentElement.setAttribute('data-theme', theme);
  }, [globalState.theme]);

  return (
    <ThemeProvider>
      {children}
    </ThemeProvider>
  );
};
